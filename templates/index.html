<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Natural Language to SQL Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Natural Language to SQL Converter</h1>
        
        <form method="POST" action="/convert_to_sql">
            <label for="nlQuery">Enter your natural language query:</label>
            <textarea name="nl_query" id="nlQuery" placeholder="e.g., Show me all customers from Germany" required></textarea>
            
            <button type="submit">Convert to SQL</button>
        </form>
        
        <!-- Let's text wrap the output-->
        {% if sql_query %}
        <div class="result">
            <h3>Generated SQL:</h3>
            <pre style="white-space: pre-wrap; word-wrap: break-word;">{{ sql_query }}</pre>
            
            <form method="POST" action="/run_query">
                <input type="hidden" name="sql_query" value="{{ sql_query }}">
                <button type="submit">Run Query</button>
            </form>
        </div>
        {% endif %}
    </div>

    {% if results %}
    <div class="container">
        <div class="result">
            <h3>Query Results:</h3>
            {% if results.data %}
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        {% for column in results.columns %}
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">{{ column }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in results.data %}
                    <tr>
                        {% for cell in row %}
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ cell }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p>No results found.</p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% if results %}
    <div class="container">
        <div class="result">
            <form method="POST" action="/download_csv">
                <input type="hidden" name="sql_query" value="{{ sql_query }}">
                <button type="submit">Download CSV</button>
            </form>
        </div>
    </div>
    {% endif %}

</body>
</html>
